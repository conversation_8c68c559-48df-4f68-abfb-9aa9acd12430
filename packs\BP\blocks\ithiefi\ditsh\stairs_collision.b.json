{
  "format_version": "1.21.90",
  "minecraft:block": {
    "description": {
      "identifier": "ditsh:stairs_collision",
      "menu_category": { "category": "none", "is_hidden_in_commands": true },
      "traits": {
        "minecraft:placement_position": { "enabled_states": ["minecraft:vertical_half"] },
        "minecraft:placement_direction": { "enabled_states": ["minecraft:cardinal_direction"] }
      },
      "states": { "ditsh:corner": [false, true] }
    },
    "components": {
      "ditsh:stairs": {},
      /* Add this, if version used is 1.21.100+
      "minecraft:movable": {
        "movement_type": "popped"
      },*/
      "minecraft:geometry": "geometry.ithiefi_ditsh_blocker",
      "minecraft:liquid_detection": {
        "detection_rules": [{ "liquid_type": "water", "can_contain_liquid": true, "on_liquid_touches": "no_reaction" }]
      },
      "minecraft:material_instances": { "*": { "texture": "ditsh:empty", "render_method": "alpha_test" } },
      "minecraft:destruction_particles": { "texture": "ditsh:empty" },
      "minecraft:loot": "loot_tables/empty.json",
      "minecraft:selection_box": false,
      "minecraft:destructible_by_explosion": false,
      "minecraft:destructible_by_mining": false,
      "minecraft:flammable": false,
      "minecraft:redstone_conductivity": { "allows_redstone_to_step_down": false, "redstone_conductor": false },
      "minecraft:replaceable": {},
      "tag:tap:no_connect": {}
    },
    "permutations": [
      {
        "condition": "q.block_state('minecraft:cardinal_direction') == 'north'",
        "components": { "minecraft:transformation": { "rotation": [0, 0, 0] } }
      },
      {
        "condition": "q.block_state('minecraft:cardinal_direction') == 'west'",
        "components": { "minecraft:transformation": { "rotation": [0, 90, 0] } }
      },
      {
        "condition": "q.block_state('minecraft:cardinal_direction') == 'south'",
        "components": { "minecraft:transformation": { "rotation": [0, 180, 0] } }
      },
      {
        "condition": "q.block_state('minecraft:cardinal_direction') == 'east'",
        "components": { "minecraft:transformation": { "rotation": [0, -90, 0] } }
      },

      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && !q.block_state('ditsh:corner')",
        "components": { "minecraft:collision_box": { "origin": [-8, 0, -8], "size": [16, 0.001, 8] } }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && !q.block_state('ditsh:corner')",
        "components": { "minecraft:collision_box": { "origin": [-8, 15.999, -8], "size": [16, 0.001, 8] } }
      },

      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('ditsh:corner')",
        "components": { "minecraft:collision_box": { "origin": [-8, 0, -8], "size": [8, 0.001, 8] } }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('ditsh:corner')",
        "components": { "minecraft:collision_box": { "origin": [-8, 15.999, -8], "size": [8, 0.001, 8] } }
      }
    ]
  }
}
