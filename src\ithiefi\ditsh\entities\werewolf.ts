import { <PERSON><PERSON><PERSON>, Player, Vector3, GameMode, EntityDamageCause, system } from "@minecraft/server";
import { getDistance } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";

/**
 * @fileoverview Werewolf Entity Handler for DitSH Add-On
 *
 * This module handles the werewolf entity behavior including:
 * - Gradual health drain for players looking at it (after 3 seconds)
 * - Teleportation to players NOT looking at it
 * - Player look time tracking and reset mechanisms
 * - Player detection and line-of-sight checking using fixed length raycast
 *
 * <AUTHOR>
 * @version 2.0.0
 */

/** Damage dealt to players when werewolf teleports to them (5 hearts = 10 damage) */
const WEREWOLF_DAMAGE: number = 10;

/** Detection radius for players (128 blocks) */
const DETECTION_RADIUS: number = 128;

/** Raycast parameters for line-of-sight detection */
const RAYCAST_RADIUS: number = 5;
const RAYCAST_STEP: number = 2;

/** Minimum look duration before damage starts (3 seconds = 60 ticks) */
const DAMAGE_START_TICKS: number = 60;

/** Damage per event trigger when looking at werewolf */
const LOOK_DAMAGE: number = 4;

/** Teleportation chance (out of 100) when not_looked_at_event triggers */
const TELEPORT_CHANCE: number = 3;

/** Map to track when each player started looking at the werewolf */
const playerLookStartTimes: Map<string, number> = new Map();

/**
 * Checks if a player is looking at the werewolf using fixed length raycast.
 * Uses 2-block step size and 5-block radius detection per raycast point.
 *
 * @param player - The player to check
 * @param werewolfLocation - The werewolf's current location
 * @returns True if the player is looking at the werewolf
 */
function isPlayerLookingAtWerewolf(player: Player, werewolfLocation: Vector3): boolean {
  try {
    const playerLocation: Vector3 = {
      x: player.location.x,
      y: player.location.y + 1.6, // Eye level
      z: player.location.z
    };

    const viewDirection: Vector3 = player.getViewDirection();
    const maxDistance: number = getDistance(playerLocation, werewolfLocation);
    const raycastStep: number = RAYCAST_STEP;
    const detectionRadius: number = RAYCAST_RADIUS;

    // Perform fixed length raycast from player's head location
    const raycastPoints: Vector3[] = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);

    // Check each raycast point for proximity to werewolf
    for (const rayPoint of raycastPoints) {
      const distanceToWerewolf: number = getDistance(rayPoint, werewolfLocation);

      if (distanceToWerewolf <= detectionRadius) {
        // Check if the raycast point is within the vertical detection area (3 blocks high)
        if (rayPoint.y >= werewolfLocation.y && rayPoint.y <= werewolfLocation.y + 3.0) {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Handles health drain for players looking at the werewolf.
 * Damages players every time the looked_at_event is triggered after they've been looking for 3+ seconds continuously.
 * The timer resets if a player stops looking at the werewolf.
 *
 * @param werewolf - The werewolf entity
 */
export function werewolfDamageLookingPlayer(werewolf: Entity): void {
  try {
    const werewolfLocation: Vector3 = werewolf.location;
    const currentTick: number = system.currentTick;

    // Get all valid players within detection radius
    const players: Player[] = werewolf.dimension.getPlayers({
      location: werewolfLocation,
      maxDistance: DETECTION_RADIUS,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    // Check each player to see if they're looking at the werewolf
    for (const player of players) {
      const playerId: string = player.id;

      if (isPlayerLookingAtWerewolf(player, werewolfLocation)) {
        // Player is looking at werewolf
        if (!playerLookStartTimes.has(playerId)) {
          // First time looking - record start time
          playerLookStartTimes.set(playerId, currentTick);
        } else {
          // Player has been looking - check if enough time has passed for damage
          const lookStartTick: number = playerLookStartTimes.get(playerId)!;
          const lookDuration: number = currentTick - lookStartTick;

          if (lookDuration >= DAMAGE_START_TICKS) {
            // Player has been looking for 3+ seconds, damage them every event trigger
            player.applyDamage(LOOK_DAMAGE, { cause: EntityDamageCause.entityAttack, damagingEntity: werewolf });
          }
        }
      } else {
        // Player is not looking at werewolf - reset their timer
        if (playerLookStartTimes.has(playerId)) {
          playerLookStartTimes.delete(playerId);
        }
      }
    }
  } catch (error) {
    console.warn(`Failed to handle werewolf damage looking player: ${error}`);
  }
}

/**
 * Handles werewolf teleportation to a player who is NOT looking at it.
 * Has a chance to teleport to a random player not looking at the werewolf and deals 10 hearts of damage.
 *
 * @param werewolf - The werewolf entity
 */
export function werewolfTeleportAndKill(werewolf: Entity): void {
  try {
    // Roll for teleportation chance
    const randomChance: number = Math.floor(Math.random() * 100) + 1;
    if (randomChance > TELEPORT_CHANCE) {
      // No teleportation this time
      return;
    }

    const werewolfLocation: Vector3 = werewolf.location;

    // Get all valid players within detection radius
    const players: Player[] = werewolf.dimension.getPlayers({
      location: werewolfLocation,
      maxDistance: DETECTION_RADIUS,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    // Find players NOT looking at the werewolf
    const playersNotLooking: Player[] = [];
    for (const player of players) {
      if (!isPlayerLookingAtWerewolf(player, werewolfLocation)) {
        playersNotLooking.push(player);
      }
    }

    // If there are players not looking, teleport to one of them
    if (playersNotLooking.length > 0) {
      // Select a random player not looking at the werewolf
      const targetPlayer: Player = playersNotLooking[Math.floor(Math.random() * playersNotLooking.length)]!;

      // Teleport werewolf directly to the player (keeping existing direct teleport behavior)
      werewolf.teleport(targetPlayer.location);

      // Immediately damage the player for 10 hearts
      targetPlayer.applyDamage(WEREWOLF_DAMAGE, { cause: EntityDamageCause.entityAttack, damagingEntity: werewolf });

      // Play kill sound effect
      werewolf.dimension.playSound("mob.ditsh.werewolf.kill", targetPlayer.location);
    }
  } catch (error) {
    console.warn(`Failed to handle werewolf teleport and kill: ${error}`);
  }
}

/**
 * Resets the look tracking for players who stop looking at the werewolf.
 * This function is triggered by the not_looked_at_event and clears the look start time
 * for players who are no longer looking at the werewolf.
 *
 * @param werewolf - The werewolf entity
 */
export function werewolfResetPlayerLookTime(werewolf: Entity): void {
  try {
    const werewolfLocation: Vector3 = werewolf.location;

    // Get all valid players within detection radius
    const players: Player[] = werewolf.dimension.getPlayers({
      location: werewolfLocation,
      maxDistance: DETECTION_RADIUS,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    // Check each player and reset tracking for those not looking
    for (const player of players) {
      const playerId: string = player.id;

      if (!isPlayerLookingAtWerewolf(player, werewolfLocation)) {
        // Player is not looking at werewolf - reset their tracking data
        if (playerLookStartTimes.has(playerId)) {
          playerLookStartTimes.delete(playerId);
        }
      }
    }

    // Also clean up tracking data for players who are no longer in range
    const validPlayerIds: Set<string> = new Set(players.map((p) => p.id));

    // Clean up look start times for players not in range
    for (const [playerId] of playerLookStartTimes) {
      if (!validPlayerIds.has(playerId)) {
        playerLookStartTimes.delete(playerId);
      }
    }
  } catch (error) {
    console.warn(`Failed to reset werewolf player look time: ${error}`);
  }
}
