{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.ithiefi_ditsh_specimen9", "texture_width": 96, "texture_height": 96, "visible_bounds_width": 3, "visible_bounds_height": 4, "visible_bounds_offset": [0, 1, 0]}, "bones": [{"name": "root", "pivot": [0, 0, 0]}, {"name": "torso", "parent": "root", "pivot": [0, 0, 0], "cubes": [{"origin": [-5, 0, -5], "size": [10, 18, 10], "uv": [0, 23]}]}, {"name": "head", "parent": "torso", "pivot": [0, 14.4375, -2.09375], "cubes": [{"origin": [-6, 15, -12], "size": [12, 3, 14], "uv": [41, 23]}, {"origin": [-1, 19, -12.75], "size": [2, 4, 2], "pivot": [0, 21, -11.75], "rotation": [-22.5, 0, 0], "uv": [22, 54]}, {"origin": [-5, 18, -12], "size": [10, 4, 3], "uv": [41, 41]}, {"origin": [5, 18, -12], "size": [2, 11, 3], "uv": [11, 54]}, {"origin": [-7, 18, -9], "size": [14, 11, 11], "uv": [0, 0]}, {"origin": [-2, 22, -12], "size": [4, 4, 3], "uv": [51, 13]}, {"origin": [-5, 26, -12], "size": [10, 3, 3], "uv": [41, 49]}, {"origin": [-7, 18, -12], "size": [2, 11, 3], "uv": [0, 54]}]}, {"name": "mouth", "parent": "head", "pivot": [0, 15, -10]}, {"name": "snout", "parent": "mouth", "pivot": [0, 16.0628, -11.13043], "cubes": [{"origin": [-4, 14, -13], "size": [1, 1, 1], "pivot": [-4, 16, -12], "rotation": [22.5, 0, 0], "uv": [46, 59]}, {"origin": [-4, 14, -15], "size": [1, 1, 1], "pivot": [-4, 16, -12], "rotation": [22.5, 0, 0], "uv": [41, 59]}, {"origin": [-2, 14, -15], "size": [1, 1, 1], "pivot": [-4, 16, -12], "rotation": [22.5, 0, 0], "uv": [51, 59]}, {"origin": [-4, 15, -15], "size": [8, 1, 3], "pivot": [-4, 16, -12], "rotation": [22.5, 0, 0], "uv": [51, 8]}, {"origin": [1, 14, -15], "size": [1, 1, 1], "pivot": [-4, 16, -12], "rotation": [22.5, 0, 0], "uv": [56, 59]}, {"origin": [3, 14, -15], "size": [1, 1, 1], "pivot": [-4, 16, -12], "rotation": [22.5, 0, 0], "uv": [31, 60]}, {"origin": [3, 14, -13], "size": [1, 1, 1], "pivot": [-4, 16, -12], "rotation": [22.5, 0, 0], "uv": [36, 60]}]}, {"name": "jaw", "parent": "mouth", "pivot": [-0.05556, 14.91667, -8.38889], "cubes": [{"origin": [-4, 13.5, -10], "size": [1, 1, 1], "uv": [31, 54]}, {"origin": [-4, 11.75, -10], "size": [1, 1, 1], "uv": [36, 54]}, {"origin": [-4, 10, -10], "size": [1, 1, 1], "uv": [41, 56]}, {"origin": [-2, 10, -10], "size": [1, 1, 1], "uv": [46, 56]}, {"origin": [0.5, 10, -10], "size": [1, 1, 1], "uv": [51, 56]}, {"origin": [3, 10, -10], "size": [1, 1, 1], "uv": [56, 56]}, {"origin": [3, 11.75, -10], "size": [1, 1, 1], "uv": [36, 57]}, {"origin": [3, 13.5, -10], "size": [1, 1, 1], "uv": [31, 57]}, {"origin": [-4.5, 9.75, -9], "size": [9, 6, 1], "uv": [51, 0]}]}]}]}