import { GameMode, EntityDamageCause, system } from "@minecraft/server";
import { getDistance } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";
const WEREWOLF_DAMAGE = 10;
const DETECTION_RADIUS = 128;
const RAYCAST_RADIUS = 5;
const RAYCAST_STEP = 2;
const DAMAGE_START_TICKS = 60;
const LOOK_DAMAGE = 4;
const TELEPORT_CHANCE = 3;
const playerLookStartTimes = new Map();
function isPlayerLookingAtWerewolf(player, werewolfLocation) {
    try {
        const playerLocation = {
            x: player.location.x,
            y: player.location.y + 1.6,
            z: player.location.z
        };
        const viewDirection = player.getViewDirection();
        const maxDistance = getDistance(playerLocation, werewolfLocation);
        const raycastStep = RAYCAST_STEP;
        const detectionRadius = RAYCAST_RADIUS;
        const raycastPoints = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);
        for (const rayPoint of raycastPoints) {
            const distanceToWerewolf = getDistance(rayPoint, werewolfLocation);
            if (distanceToWerewolf <= detectionRadius) {
                if (rayPoint.y >= werewolfLocation.y && rayPoint.y <= werewolfLocation.y + 3.0) {
                    return true;
                }
            }
        }
        return false;
    }
    catch (error) {
        return false;
    }
}
export function werewolfDamageLookingPlayer(werewolf) {
    try {
        const werewolfLocation = werewolf.location;
        const currentTick = system.currentTick;
        const players = werewolf.dimension.getPlayers({
            location: werewolfLocation,
            maxDistance: DETECTION_RADIUS,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        for (const player of players) {
            const playerId = player.id;
            if (isPlayerLookingAtWerewolf(player, werewolfLocation)) {
                if (!playerLookStartTimes.has(playerId)) {
                    playerLookStartTimes.set(playerId, currentTick);
                }
                else {
                    const lookStartTick = playerLookStartTimes.get(playerId);
                    const lookDuration = currentTick - lookStartTick;
                    if (lookDuration >= DAMAGE_START_TICKS) {
                        player.applyDamage(LOOK_DAMAGE, { cause: EntityDamageCause.entityAttack, damagingEntity: werewolf });
                    }
                }
            }
            else {
                if (playerLookStartTimes.has(playerId)) {
                    playerLookStartTimes.delete(playerId);
                }
            }
        }
    }
    catch (error) {
        console.warn(`Failed to handle werewolf damage looking player: ${error}`);
    }
}
export function werewolfTeleportAndKill(werewolf) {
    try {
        const randomChance = Math.floor(Math.random() * 100) + 1;
        if (randomChance > TELEPORT_CHANCE) {
            return;
        }
        const werewolfLocation = werewolf.location;
        const players = werewolf.dimension.getPlayers({
            location: werewolfLocation,
            maxDistance: DETECTION_RADIUS,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        const playersNotLooking = [];
        for (const player of players) {
            if (!isPlayerLookingAtWerewolf(player, werewolfLocation)) {
                playersNotLooking.push(player);
            }
        }
        if (playersNotLooking.length > 0) {
            const targetPlayer = playersNotLooking[Math.floor(Math.random() * playersNotLooking.length)];
            werewolf.teleport(targetPlayer.location);
            targetPlayer.applyDamage(WEREWOLF_DAMAGE, { cause: EntityDamageCause.entityAttack, damagingEntity: werewolf });
            werewolf.dimension.playSound("mob.ditsh.werewolf.kill", targetPlayer.location);
        }
    }
    catch (error) {
        console.warn(`Failed to handle werewolf teleport and kill: ${error}`);
    }
}
export function werewolfResetPlayerLookTime(werewolf) {
    try {
        const werewolfLocation = werewolf.location;
        const players = werewolf.dimension.getPlayers({
            location: werewolfLocation,
            maxDistance: DETECTION_RADIUS,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        for (const player of players) {
            const playerId = player.id;
            if (!isPlayerLookingAtWerewolf(player, werewolfLocation)) {
                if (playerLookStartTimes.has(playerId)) {
                    playerLookStartTimes.delete(playerId);
                }
            }
        }
        const validPlayerIds = new Set(players.map((p) => p.id));
        for (const [playerId] of playerLookStartTimes) {
            if (!validPlayerIds.has(playerId)) {
                playerLookStartTimes.delete(playerId);
            }
        }
    }
    catch (error) {
        console.warn(`Failed to reset werewolf player look time: ${error}`);
    }
}
