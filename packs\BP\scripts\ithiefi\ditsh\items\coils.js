import { world } from "@minecraft/server";
export const COIL_CONFIGS = new Map([
    [
        "ditsh:insane_decay_coil",
        {
            soundId: "item.ditsh.insane_decay_coil.use",
            soundInterval: 5,
            effectId: "speed",
            effectAmplifier: 3,
            effectDuration: 40
        }
    ],
    [
        "ditsh:special_coil",
        {
            soundId: "item.ditsh.special_coil.use",
            soundInterval: 2,
            effectId: "speed",
            effectAmplifier: 1,
            effectDuration: 40
        }
    ],
    [
        "ditsh:speed_coil",
        {
            soundId: "item.ditsh.speed_coil.use",
            soundInterval: 3,
            effectId: "speed",
            effectAmplifier: 0,
            effectDuration: 40
        }
    ]
]);
const playersHoldingCoils = new Set();
function handleCoilSoundEffect(player, itemId) {
    try {
        const config = COIL_CONFIGS.get(itemId);
        if (!config) {
            return;
        }
        const timerProperty = `ditsh:${itemId.split(":")[1]}_sound_timer`;
        const soundTimer = player.getDynamicProperty(timerProperty) ?? 0;
        if (soundTimer === 0 || soundTimer >= config.soundInterval) {
            player.runCommand(`playsound ${config.soundId} @s ~ ~ ~ 1.0 1.0`);
            player.setDynamicProperty(timerProperty, 1);
        }
        else {
            player.setDynamicProperty(timerProperty, soundTimer + 1);
        }
    }
    catch (error) {
        console.warn(`Failed to handle coil sound effect for player ${player.name}: ${error}`);
    }
}
function handleCoilEffect(player, itemId) {
    try {
        const config = COIL_CONFIGS.get(itemId);
        if (!config) {
            return;
        }
        player.addEffect(config.effectId, config.effectDuration, {
            amplifier: config.effectAmplifier,
            showParticles: false
        });
    }
    catch (error) {
        console.warn(`Failed to handle coil effect for player ${player.name}: ${error}`);
    }
}
export function handleCoilForPlayer(player, itemId) {
    try {
        playersHoldingCoils.add(player.id);
        handleCoilEffect(player, itemId);
        handleCoilSoundEffect(player, itemId);
    }
    catch (error) {
        console.warn(`Failed to handle coil for player ${player.name}: ${error}`);
    }
}
export function cleanupCoilTrackers() {
    try {
        const allPlayers = world.getAllPlayers();
        const currentHolders = new Set(playersHoldingCoils);
        playersHoldingCoils.clear();
        for (const player of allPlayers) {
            const playerId = player.id;
            if (!currentHolders.has(playerId)) {
                for (const [itemId] of COIL_CONFIGS) {
                    const timerProperty = `ditsh:${itemId.split(":")[1]}_sound_timer`;
                    const currentTimer = player.getDynamicProperty(timerProperty);
                    if (currentTimer !== undefined && currentTimer > 0) {
                        player.setDynamicProperty(timerProperty, 0);
                    }
                }
            }
        }
    }
    catch (error) {
        console.warn(`Failed to cleanup coil trackers: ${error}`);
    }
}
