# Coil Selection Sound System

## Overview
This system plays coil sounds immediately when a player selects a coil in their hotbar, providing instant audio feedback separate from the existing interval-based sound system.

## Implementation Details

### New Functions Added

#### `playCoilSelectionSound(player: Player, itemId: string)`
- **Purpose**: Plays a coil sound immediately when selected
- **Location**: `src/ithiefi/ditsh/items/coils.ts`
- **Features**:
  - Uses the same sound IDs as the interval system for consistency
  - Wrapped in `system.run()` for proper timing
  - Includes error handling and logging
  - Only plays for the specific player who selected the coil

#### `initCoilHotbarListener()`
- **Purpose**: Initializes the hotbar selection event listener
- **Location**: `src/ithiefi/ditsh/items/coils.ts`
- **Features**:
  - Subscribes to `playerHotbarSelectedSlotChange` event
  - Checks if selected item is a registered coil
  - Calls `playCoilSelectionSound()` for valid coils
  - Includes comprehensive error handling and logging

### Integration Points

#### Main System Integration
- **File**: `src/ithiefi/ditsh/main.ts`
- **Changes**:
  - Added import: `import { initCoilHotbarListener } from "./items/coils";`
  - Added initialization call: `initCoilHotbarListener();`
  - Updated documentation to include the new system

## How It Works

1. **Initialization**: When the add-on loads, `initCoilHotbarListener()` is called from `main.ts`
2. **Event Listening**: The system subscribes to `world.afterEvents.playerHotbarSelectedSlotChange`
3. **Coil Detection**: When a player changes hotbar slots, the system checks if the new item is a coil
4. **Sound Playback**: If it's a coil, `playCoilSelectionSound()` immediately plays the corresponding sound
5. **Logging**: Console messages help track system activity for debugging

## Supported Coils

The system automatically works with all coils registered in `COIL_CONFIGS`:

- `ditsh:insane_decay_coil` - Sound: `item.ditsh.insane_decay_coil.use`
- `ditsh:special_coil` - Sound: `item.ditsh.special_coil.use`
- `ditsh:speed_coil` - Sound: `item.ditsh.speed_coil.use`

## Testing Instructions

### In-Game Testing
1. Load the world with the DitSH add-on
2. Give yourself coil items: `/give @s ditsh:speed_coil`
3. Place coils in different hotbar slots
4. Switch between hotbar slots containing coils
5. **Expected Result**: Each time you select a coil, you should hear its sound immediately

### Console Verification
Check the console for these messages:
- `DitSH: Initializing coil hotbar selection listener...`
- `DitSH: Coil hotbar selection listener initialized successfully`
- `DitSH: Player [name] selected coil [itemId] in slot [number]`
- `DitSH: Playing selection sound [soundId] for player [name]`
- `DitSH: Successfully played selection sound for [itemId]`

### Troubleshooting
- **No sound**: Check if the coil is registered in `COIL_CONFIGS`
- **Console errors**: Look for error messages starting with "Failed to..."
- **Event not firing**: Verify the hotbar selection event is supported in your Minecraft version

## System Separation

This system is completely separate from the existing interval-based sound system:

- **Selection sounds**: Play once immediately when coil is selected (this system)
- **Interval sounds**: Play repeatedly while holding coil (existing system)
- **No conflicts**: Both systems can operate simultaneously without interference

## Future Enhancements

To add new coils to this system:
1. Add the coil configuration to `COIL_CONFIGS` in `coils.ts`
2. Register the item handler in `ITEM_HANDLERS` in `items/index.ts`
3. The selection sound system will automatically work with the new coil

## Technical Notes

- Uses Minecraft Bedrock Script API `playerHotbarSelectedSlotChange` event
- Event is marked as pre-release, so signature may change in future versions
- System includes comprehensive error handling to prevent crashes
- Logging can be removed in production by removing console.log statements
