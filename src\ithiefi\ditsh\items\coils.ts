/**
 * @fileoverview Coil Items Effect and Sound System
 *
 * This module handles all coil-related functionality including sound effects, visual effects,
 * and player state management for the DitSH add-on coil items. It provides a centralized
 * system for managing different coil types with configurable effects and sound timing.
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2.0.0
 *
 * @remarks
 * This system uses a configuration-driven approach where each coil type is defined with:
 * - Sound effect ID and timing interval
 * - Effect type, amplifier, and duration
 * - Centralized state tracking for cleanup
 *
 * The system automatically handles:
 * - Sound timing using dynamic properties per player per coil
 * - Effect application with proper amplifiers and durations
 * - Player state tracking for cleanup purposes
 * - Memory management to prevent leaks
 *
 * @example
 * ```typescript
 * // In index.ts
 * import { handleCoilForPlayer, cleanupCoilTrackers } from "./coils";
 *
 * // Handle coil effects
 * handleCoilForPlayer(player, "ditsh:speed_coil");
 *
 * // Cleanup (called periodically)
 * cleanupCoilTrackers();
 * ```
 */

import { Player, world } from "@minecraft/server";

/**
 * Configuration interface for coil items
 * Defines all the properties needed for a coil's behavior
 */
export interface CoilConfig {
  /** Sound effect identifier to play */
  soundId: string;
  /** Interval in seconds between sound plays */
  soundInterval: number;
  /** Effect identifier to apply to the player */
  effectId: string;
  /** Effect amplifier level (0 = level 1, 1 = level 2, etc.) */
  effectAmplifier: number;
  /** Effect duration in ticks (20 ticks = 1 second) */
  effectDuration: number;
}

/**
 * Registry of coil configurations
 * Maps coil item IDs to their respective configurations
 *
 * @remarks
 * Each coil has unique settings for:
 * - Sound timing (different intervals for variety)
 * - Effect strength (different amplifiers for balance)
 * - Effect duration (consistent 2-second duration for smooth gameplay)
 */
export const COIL_CONFIGS: Map<string, CoilConfig> = new Map([
  [
    "ditsh:insane_decay_coil",
    {
      soundId: "item.ditsh.insane_decay_coil.use",
      soundInterval: 5, // Every 5 seconds
      effectId: "speed",
      effectAmplifier: 3, // Swiftness IV
      effectDuration: 40 // 2 seconds
    }
  ],
  [
    "ditsh:special_coil",
    {
      soundId: "item.ditsh.special_coil.use",
      soundInterval: 2, // Every 2 seconds
      effectId: "speed",
      effectAmplifier: 1, // Swiftness II
      effectDuration: 40 // 2 seconds
    }
  ],
  [
    "ditsh:speed_coil",
    {
      soundId: "item.ditsh.speed_coil.use",
      soundInterval: 3, // Every 3 seconds
      effectId: "speed",
      effectAmplifier: 0, // Swiftness I
      effectDuration: 40 // 2 seconds
    }
  ]
]);

/**
 * Set to track players currently holding any coil item
 * Used for cleanup when players stop holding items
 *
 * @remarks
 * - Populated by handleCoilForPlayer when players hold coils
 * - Cleared and repopulated each cycle for accurate tracking
 * - Used by cleanup functions to reset timers for players who stopped holding coils
 */
const playersHoldingCoils: Set<string> = new Set();

/**
 * Handles sound effects for coil items.
 * Manages timing and playback using dynamic properties per player per coil type.
 *
 * @param player - The player holding the coil
 * @param itemId - The coil item identifier
 *
 * @description This function:
 * - Gets the coil configuration for sound settings
 * - Manages individual timers for each coil type per player
 * - Plays sound immediately when coil is first equipped (timer = 0)
 * - Plays sound at the configured interval for subsequent plays
 * - Resets timer after sound playback
 *
 * @remarks
 * - Uses dynamic properties to persist timer state across ticks
 * - Each coil type has its own timer property to avoid conflicts
 * - Sound intervals are configurable per coil type for variety
 * - Sounds are played only to the specific player holding the coil
 * - First equip triggers immediate sound, then follows normal interval timing
 *
 * @example
 * ```typescript
 * // Called from handleCoilForPlayer
 * handleCoilSoundEffect(player, "ditsh:speed_coil");
 * ```
 */
function handleCoilSoundEffect(player: Player, itemId: string): void {
  try {
    const config = COIL_CONFIGS.get(itemId);
    if (!config) {
      return; // No configuration found for this item
    }

    // Create unique timer property for this coil type
    const timerProperty = `ditsh:${itemId.split(":")[1]}_sound_timer`;

    // Get current timer value
    const soundTimer: number = (player.getDynamicProperty(timerProperty) as number) ?? 0;

    // Check if this is the first time holding the coil (timer = 0) or if the configured interval has passed
    if (soundTimer === 0 || soundTimer >= config.soundInterval) {
      // Play the sound effect for this specific player only
      player.runCommand(`playsound ${config.soundId} @s ~ ~ ~ 1.0 1.0`);

      // Set timer to 1 to start the interval countdown (not 0 to avoid immediate replay)
      player.setDynamicProperty(timerProperty, 1);
    } else {
      // Increment the timer by 1 second (since this runs every second)
      player.setDynamicProperty(timerProperty, soundTimer + 1);
    }
  } catch (error) {
    console.warn(`Failed to handle coil sound effect for player ${player.name}: ${error}`);
  }
}

/**
 * Handles effect application for coil items.
 * Applies the configured effect with proper amplifier and duration.
 *
 * @param player - The player holding the coil
 * @param itemId - The coil item identifier
 *
 * @description This function:
 * - Gets the coil configuration for effect settings
 * - Applies the configured effect with proper amplifier and duration
 * - Uses showParticles: false to avoid visual clutter
 *
 * @remarks
 * - Effect settings are configurable per coil type
 * - Duration ensures no gaps between applications (40 ticks = 2 seconds)
 * - Amplifier determines effect strength (0 = level 1, 1 = level 2, etc.)
 * - Particles are disabled to maintain clean visual experience
 *
 * @example
 * ```typescript
 * // Called from handleCoilForPlayer
 * handleCoilEffect(player, "ditsh:special_coil");
 * ```
 */
function handleCoilEffect(player: Player, itemId: string): void {
  try {
    const config = COIL_CONFIGS.get(itemId);
    if (!config) {
      return; // No configuration found for this item
    }

    // Apply the configured effect
    player.addEffect(config.effectId, config.effectDuration, {
      amplifier: config.effectAmplifier,
      showParticles: false // Don't show particles to avoid visual clutter
    });
  } catch (error) {
    console.warn(`Failed to handle coil effect for player ${player.name}: ${error}`);
  }
}

/**
 * Main coil handler that manages both effects and sounds for all coil types.
 * This is the primary entry point for coil functionality.
 *
 * @param player - The player holding the coil
 * @param itemId - The coil item identifier
 *
 * @description This function:
 * - Tracks that the player is currently holding a coil
 * - Applies the appropriate effect for the coil type
 * - Manages sound timing and playback
 * - Provides unified handling for all coil functionality
 *
 * @remarks
 * - This replaces individual coil handler functions
 * - Centralizes all coil logic in one place
 * - Maintains consistent behavior across all coil types
 * - Easy to extend for new coil types by adding to COIL_CONFIGS
 *
 * @example
 * ```typescript
 * // Called from the item handler registry
 * handleCoilForPlayer(player, "ditsh:insane_decay_coil");
 * ```
 */
export function handleCoilForPlayer(player: Player, itemId: string): void {
  try {
    // Track that this player is currently holding a coil
    playersHoldingCoils.add(player.id);

    // Handle effect application
    handleCoilEffect(player, itemId);

    // Handle sound timing and playback
    handleCoilSoundEffect(player, itemId);
  } catch (error) {
    console.warn(`Failed to handle coil for player ${player.name}: ${error}`);
  }
}
/**
 * Cleans up coil-related tracking data and resets timers.
 * This function should be called periodically to prevent memory leaks.
 *
 * @description This function:
 * - Identifies players who are no longer holding coils
 * - Resets sound timers for players who stopped holding coils
 * - Clears the tracking set for the next cycle
 * - Maintains optimal performance by cleaning up unused data
 *
 * @remarks
 * - Should be called from the main cleanup system
 * - Safe to call frequently as it only processes tracking data
 * - Automatically handles timer cleanup for all coil types
 * - Prevents memory leaks from accumulated dynamic properties
 *
 * @example
 * ```typescript
 * // Called from cleanupAllItemTrackers in index.ts
 * cleanupCoilTrackers();
 * ```
 */
export function cleanupCoilTrackers(): void {
  try {
    // Get all current players
    const allPlayers: Player[] = world.getAllPlayers();

    // Clean up players who are no longer holding any coils
    const currentHolders: Set<string> = new Set(playersHoldingCoils);
    playersHoldingCoils.clear(); // Clear the set, it will be repopulated by active handlers

    // Reset sound timers for players who stopped holding coils
    for (const player of allPlayers) {
      const playerId: string = player.id;

      // If player was not holding any coil this cycle, reset all their coil timers
      if (!currentHolders.has(playerId)) {
        // Reset timers for all coil types
        for (const [itemId] of COIL_CONFIGS) {
          const timerProperty = `ditsh:${itemId.split(":")[1]}_sound_timer`;
          const currentTimer = player.getDynamicProperty(timerProperty) as number;
          if (currentTimer !== undefined && currentTimer > 0) {
            player.setDynamicProperty(timerProperty, 0);
          }
        }
      }
    }
  } catch (error) {
    console.warn(`Failed to cleanup coil trackers: ${error}`);
  }
}

/**
 * @fileoverview Adding New Coil Types
 *
 * To add a new coil type to the system:
 *
 * 1. **Add coil configuration** to the COIL_CONFIGS map:
 *    ```typescript
 *    ["ditsh:your_new_coil", {
 *      soundId: "item.ditsh.your_new_coil.use",
 *      soundInterval: 4, // seconds between sound plays
 *      effectId: "speed", // or other effect like "strength", "regeneration"
 *      effectAmplifier: 2, // effect level (0 = level 1, 1 = level 2, etc.)
 *      effectDuration: 40 // ticks (40 = 2 seconds)
 *    }]
 *    ```
 *
 * 2. **Register in the main item system** (index.ts):
 *    ```typescript
 *    ["ditsh:your_new_coil", (player: Player) => handleCoilForPlayer(player, "ditsh:your_new_coil")]
 *    ```
 *
 * 3. **Create the item definition** in `packs/BP/items/ithiefi/ditsh/your_new_coil.json`
 *
 * 4. **Add sound definition** in `packs/RP/sounds/sound_definitions.json`
 *
 * 5. **Create attachable** in `packs/RP/attachables/ithiefi/ditsh/your_new_coil.json`
 *
 * 6. **Add translation** in `packs/RP/texts/en_US.lang`
 *
 * The system will automatically handle all the mechanics for your new coil!
 */
