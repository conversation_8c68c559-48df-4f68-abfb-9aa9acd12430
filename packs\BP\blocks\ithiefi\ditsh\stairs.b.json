{"format_version": "1.21.70", "minecraft:block": {"description": {"identifier": "ditsh:stairs", "menu_category": {"category": "construction"}, "traits": {"minecraft:placement_position": {"enabled_states": ["minecraft:vertical_half"]}, "minecraft:placement_direction": {"enabled_states": ["minecraft:cardinal_direction"]}}, "states": {"ditsh:type": [1, 2, 3, 4, 5]}}, "components": {"ditsh:stairs": {}, "minecraft:item_visual": {"geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": false, "top_sw": false}}, "material_instances": {"*": {"texture": "ditsh_stairs"}}}, "minecraft:material_instances": {"*": {"texture": "ditsh_stairs"}}, "tag:ditsh:stairs": {}, "tag:minecraft:is_pickaxe_item_destructible": {}, "tag:stone": {}}, "permutations": [{"condition": "q.block_state('minecraft:vertical_half') == 'bottom'", "components": {"minecraft:collision_box": {"origin": [-8, 0, -8], "size": [16, 8, 16]}, "minecraft:selection_box": {"origin": [-8, 0, -8], "size": [16, 8, 16]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top'", "components": {"minecraft:collision_box": {"origin": [-8, 8, -8], "size": [16, 8, 16]}, "minecraft:selection_box": {"origin": [-8, 8, -8], "size": [16, 8, 16]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 1", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": false, "top_sw": false}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down", "north"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 1", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": false, "top_nw": false, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down", "south"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 1", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": false, "top_se": true, "top_sw": false}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down", "east"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 1", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": false, "top_nw": true, "top_se": false, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down", "west"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 2", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": false, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down", "north", "west"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 2", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": false, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down", "south", "east"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 2", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": false}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down", "north", "east"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 2", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": false, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down", "south", "west"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 3", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": false}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down", "north", "east"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 3", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": false, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down", "south", "west"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 3", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": false, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down", "south", "east"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 3", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": false, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down", "north", "west"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 4", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": false, "top_nw": true, "top_se": false, "top_sw": false}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 4", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": false, "top_nw": false, "top_se": false, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 4", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": false, "top_nw": false, "top_se": true, "top_sw": false}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 4", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": false, "top_nw": false, "top_se": false, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 5", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": false, "top_se": false, "top_sw": false}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 5", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": false, "top_nw": false, "top_se": true, "top_sw": false}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 5", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": false, "top_se": false, "top_sw": false}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 5", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": false, "top_nw": true, "top_se": false, "top_sw": false}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 1", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": false, "bot_sw": false, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up", "north"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 1", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": false, "bot_nw": false, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up", "south"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 1", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": false, "bot_se": true, "bot_sw": false, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up", "east"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 1", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": false, "bot_nw": true, "bot_se": false, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up", "west"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 2", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": false, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up", "north", "west"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 2", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": false, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up", "south", "east"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 2", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": false, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up", "south", "west"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 2", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": false, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up", "north", "east"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 3", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": true, "bot_sw": false, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up", "north", "east"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 3", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": false, "bot_nw": true, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up", "south", "west"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 3", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": false, "bot_se": true, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up", "south", "east"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 3", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": true, "bot_se": false, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up", "north", "west"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 4", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": false, "bot_nw": true, "bot_se": false, "bot_sw": false, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 4", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": false, "bot_nw": false, "bot_se": false, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 4", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": false, "bot_nw": false, "bot_se": true, "bot_sw": false, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 4", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": false, "bot_nw": false, "bot_se": false, "bot_sw": true, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 5", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": false, "bot_se": false, "bot_sw": false, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 5", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": false, "bot_nw": false, "bot_se": true, "bot_sw": false, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 5", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": true, "bot_nw": false, "bot_se": false, "bot_sw": false, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"]}]}}}, {"condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 5", "components": {"minecraft:geometry": {"identifier": "geometry.ithiefi_ditsh_stairs", "bone_visibility": {"bot_ne": false, "bot_nw": true, "bot_se": false, "bot_sw": false, "top_ne": true, "top_nw": true, "top_se": true, "top_sw": true}}, "minecraft:liquid_detection": {"detection_rules": [{"liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"]}]}}}]}}